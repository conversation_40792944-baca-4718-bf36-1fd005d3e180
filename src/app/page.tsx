'use client';

import { useState } from 'react';

interface FormData {
  keywords: string;
  location: string;
  distance: string;
  timePosted: string;
  sortBy: string;
  currentJobId: string;
  workType: string;
}

export default function LinkedInJobSearchGenerator() {
  const [formData, setFormData] = useState<FormData>({
    keywords: '',
    location: '',
    distance: '25',
    timePosted: 'r86400',
    sortBy: 'R',
    currentJobId: '',
    workType: ''
  });

  const [generatedUrl, setGeneratedUrl] = useState<string>('');
  const [copied, setCopied] = useState<boolean>(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const generateUrl = () => {
    const baseUrl = 'https://www.linkedin.com/jobs/search/';
    const params = new URLSearchParams();

    // Add parameters only if they have values
    if (formData.currentJobId.trim()) {
      params.append('currentJobId', formData.currentJobId.trim());
    }

    if (formData.distance) {
      params.append('distance', formData.distance);
    }

    if (formData.timePosted) {
      params.append('f_TPR', formData.timePosted);
    }

    if (formData.keywords.trim()) {
      params.append('keywords', formData.keywords.trim());
    }

    if (formData.location.trim()) {
      params.append('location', formData.location.trim());
    }

    // Add work type filter if selected
    if (formData.workType) {
      params.append('f_WT', formData.workType);
    }

    params.append('origin', 'JOB_SEARCH_PAGE_JOB_FILTER');

    if (formData.sortBy) {
      params.append('sortBy', formData.sortBy);
    }

    const url = `${baseUrl}?${params.toString()}`;
    setGeneratedUrl(url);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const openInNewTab = () => {
    if (generatedUrl) {
      window.open(generatedUrl, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            LinkedIn Job Search URL Generator
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Customize your LinkedIn job search parameters and generate a direct URL to find exactly what you're looking for.
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 mb-8">
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Keywords */}
              <div>
                <label htmlFor="keywords" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Job Keywords *
                </label>
                <input
                  type="text"
                  id="keywords"
                  name="keywords"
                  value={formData.keywords}
                  onChange={handleInputChange}
                  placeholder="e.g., Senior Operations Manager"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Enter job title or keywords you're searching for
                </p>
              </div>

              {/* Location */}
              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Location
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="e.g., United States, Florida, New York"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  City, state, or country
                </p>
              </div>

              {/* Distance */}
              <div>
                <label htmlFor="distance" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search Radius (miles)
                </label>
                <select
                  id="distance"
                  name="distance"
                  value={formData.distance}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="5">5 miles</option>
                  <option value="10">10 miles</option>
                  <option value="25">25 miles</option>
                  <option value="50">50 miles</option>
                  <option value="100">100 miles</option>
                </select>
              </div>

              {/* Time Posted */}
              <div>
                <label htmlFor="timePosted" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Time Posted
                </label>
                <select
                  id="timePosted"
                  name="timePosted"
                  value={formData.timePosted}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="r3600">Last hour</option>
                  <option value="r86400">Last 24 hours</option>
                  <option value="r604800">Last week</option>
                  <option value="r2592000">Last month</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Sort By */}
              <div>
                <label htmlFor="sortBy" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Sort Results By
                </label>
                <select
                  id="sortBy"
                  name="sortBy"
                  value={formData.sortBy}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="R">Relevance</option>
                  <option value="DD">Date Posted</option>
                </select>
              </div>

              {/* Current Job ID */}
              <div>
                <label htmlFor="currentJobId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Current Job ID (Optional)
                </label>
                <input
                  type="text"
                  id="currentJobId"
                  name="currentJobId"
                  value={formData.currentJobId}
                  onChange={handleInputChange}
                  placeholder="e.g., 4185657072"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Specific job ID if viewing a particular job
                </p>
              </div>
            </div>

            {/* Work Type Filter */}
            <div>
              <label htmlFor="workType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Work Type
              </label>
              <select
                id="workType"
                name="workType"
                value={formData.workType}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">All work types</option>
                <option value="1">On-site</option>
                <option value="2">Remote</option>
                <option value="3">Hybrid</option>
              </select>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Filter jobs by work arrangement
              </p>
            </div>

            <div className="flex justify-center">
              <button
                type="button"
                onClick={generateUrl}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Generate LinkedIn URL
              </button>
            </div>
          </form>
        </div>

        {/* Generated URL Section */}
        {generatedUrl && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Generated URL
            </h2>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-300 break-all font-mono">
                {generatedUrl}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={copyToClipboard}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                {copied ? 'Copied!' : 'Copy URL'}
              </button>

              <button
                onClick={openInNewTab}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Open in LinkedIn
              </button>
            </div>
          </div>
        )}


      </div>
    </div>
  );
}
