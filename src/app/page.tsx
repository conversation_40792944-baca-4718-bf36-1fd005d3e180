'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface FormData {
  keywords: string;
  location: string;
  distance: string;
  timePosted: string;
  sortBy: string;
  currentJobId: string;
  workType: string;
}

export default function LinkedInJobSearchGenerator() {
  const [formData, setFormData] = useState<FormData>({
    keywords: '',
    location: '',
    distance: '25',
    timePosted: 'r86400',
    sortBy: 'R',
    currentJobId: '',
    workType: ''
  });

  const [generatedUrl, setGeneratedUrl] = useState<string>('');
  const [copied, setCopied] = useState<boolean>(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const generateUrl = () => {
    const baseUrl = 'https://www.linkedin.com/jobs/search/';
    const params = new URLSearchParams();

    // Add parameters only if they have values
    if (formData.currentJobId.trim()) {
      params.append('currentJobId', formData.currentJobId.trim());
    }

    if (formData.distance) {
      params.append('distance', formData.distance);
    }

    if (formData.timePosted) {
      params.append('f_TPR', formData.timePosted);
    }

    if (formData.keywords.trim()) {
      params.append('keywords', formData.keywords.trim());
    }

    if (formData.location.trim()) {
      params.append('location', formData.location.trim());
    }

    // Add work type filter if selected
    if (formData.workType) {
      params.append('f_WT', formData.workType);
    }

    params.append('origin', 'JOB_SEARCH_PAGE_JOB_FILTER');

    if (formData.sortBy) {
      params.append('sortBy', formData.sortBy);
    }

    const url = `${baseUrl}?${params.toString()}`;
    setGeneratedUrl(url);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const openInNewTab = () => {
    if (generatedUrl) {
      window.open(generatedUrl, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            LinkedIn Job Search URL Generator
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Customize your LinkedIn job search parameters and generate a direct URL to find exactly what you&apos;re looking for.
          </p>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Job Search Parameters</CardTitle>
            <CardDescription>
              Customize your LinkedIn job search criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Keywords */}
              <div className="space-y-2">
                <Label htmlFor="keywords">
                  Job Keywords *
                </Label>
                <Input
                  id="keywords"
                  name="keywords"
                  value={formData.keywords}
                  onChange={handleInputChange}
                  placeholder="e.g., Senior Operations Manager"
                />
                <p className="text-xs text-muted-foreground">
                  Enter job title or keywords you're searching for
                </p>
              </div>

              {/* Location */}
              <div className="space-y-2">
                <Label htmlFor="location">
                  Location
                </Label>
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="e.g., United States, Florida, New York"
                />
                <p className="text-xs text-muted-foreground">
                  City, state, or country
                </p>
              </div>

              {/* Distance */}
              <div className="space-y-2">
                <Label htmlFor="distance">
                  Search Radius (miles)
                </Label>
                <Select name="distance" value={formData.distance} onValueChange={(value) => handleSelectChange('distance', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select distance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 miles</SelectItem>
                    <SelectItem value="10">10 miles</SelectItem>
                    <SelectItem value="25">25 miles</SelectItem>
                    <SelectItem value="50">50 miles</SelectItem>
                    <SelectItem value="100">100 miles</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Time Posted */}
              <div className="space-y-2">
                <Label htmlFor="timePosted">
                  Time Posted
                </Label>
                <Select name="timePosted" value={formData.timePosted} onValueChange={(value) => handleSelectChange('timePosted', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select time range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="r3600">Last hour</SelectItem>
                    <SelectItem value="r86400">Last 24 hours</SelectItem>
                    <SelectItem value="r604800">Last week</SelectItem>
                    <SelectItem value="r2592000">Last month</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Sort By */}
              <div className="space-y-2">
                <Label htmlFor="sortBy">
                  Sort Results By
                </Label>
                <Select name="sortBy" value={formData.sortBy} onValueChange={(value) => handleSelectChange('sortBy', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sort order" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="R">Relevance</SelectItem>
                    <SelectItem value="DD">Date Posted</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Current Job ID */}
              <div className="space-y-2">
                <Label htmlFor="currentJobId">
                  Current Job ID (Optional)
                </Label>
                <Input
                  id="currentJobId"
                  name="currentJobId"
                  value={formData.currentJobId}
                  onChange={handleInputChange}
                  placeholder="e.g., 4185657072"
                />
                <p className="text-xs text-muted-foreground">
                  Specific job ID if viewing a particular job
                </p>
              </div>
            </div>

            {/* Work Type Filter */}
            <div className="space-y-2">
              <Label htmlFor="workType">
                Work Type
              </Label>
              <Select name="workType" value={formData.workType} onValueChange={(value) => handleSelectChange('workType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select work type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All work types</SelectItem>
                  <SelectItem value="1">On-site</SelectItem>
                  <SelectItem value="2">Remote</SelectItem>
                  <SelectItem value="3">Hybrid</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Filter jobs by work arrangement
              </p>
            </div>

            <div className="flex justify-center">
              <Button
                type="button"
                onClick={generateUrl}
                size="lg"
                className="px-8"
              >
                Generate LinkedIn URL
              </Button>
            </div>
          </form>
          </CardContent>
        </Card>

        {/* Generated URL Section */}
        {generatedUrl && (
          <Card>
            <CardHeader>
              <CardTitle>Generated URL</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-muted rounded-lg p-4">
                <p className="text-sm break-all font-mono">
                  {generatedUrl}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={copyToClipboard}
                  variant="outline"
                  className="flex-1"
                >
                  {copied ? 'Copied!' : 'Copy URL'}
                </Button>

                <Button
                  onClick={openInNewTab}
                  className="flex-1"
                >
                  Open in LinkedIn
                </Button>
              </div>
            </CardContent>
          </Card>
        )}


      </div>
    </div>
  );
}
